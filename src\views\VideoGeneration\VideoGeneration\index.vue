<script setup lang="ts">
  import { ref, reactive, onBeforeMount, computed, onMounted } from 'vue';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendHover from '@/assets/image/base/pictures/sendHover.png.png';
  import sendDisable from '@/assets/image/base/pictures/sendDisable.png';
  import videoToImg from '@/assets/image/base/pictures/videoToImage.png';
  import uploadImage from '@/assets/image/base/pictures/upload-image.png';
  import { useThrottle } from '@/hooks/useThrottle';
  import { message, Modal } from 'ant-design-vue';
  import { createHistory, chatInHistory, fetchHistoryList, deleteHistory } from '@/api/videoGeneration';
  import {
    UploadOutlined,
    LoadingOutlined,
    CloseOutlined,
    InfoCircleOutlined,
    DownloadOutlined,
  } from '@ant-design/icons-vue';
  import { getLocalItem } from '@/utils/common';
  import { getModelList, upload } from '@/api/textToImage';
  import type { UploadProps } from 'ant-design-vue';
  import type { Message } from '../index';
  import History from '@/components/History/index.vue';
  import type { IHistoryItems } from '@/components/History/index';

  const defaultText = '帮我生成视频：小女孩在向日葵园玩耍';
  const btnImageSrc = computed(() => {
    let flag = sendBtn;
    if (inputText.value) {
      flag = loading.value ? sendDisable : sendBtnHover.value ? sendHover : sendBtn;
    } else {
      flag = sendDisable;
    }
    return flag;
  });
  // 输入框内容
  const inputText = ref(defaultText);
  const loading = ref(false);
  const sendBtnHover = ref(false);
  // 消息列表
  const state = reactive({
    messageList: [
      {
        type: 'answer',
        text: '你好，欢迎使用图文生视频功能，添加一张图片或输入你的描述，让我们一起创造属于你的视频世界吧，你可以发送输入框的内容试一下',
      },
    ] as Message[], // 消息列表
  });
  const setInputValue = (value: string) => {
    inputText.value = value;
  };

  const modelList = ref<any>([]);
  const activeModel = ref<any>({ name: 'Lightricks/LTX-Video' });
  const historyShow = ref(false);
  const historyList = ref<IHistoryItems[]>([]);
  const currentSelectedId = ref<string>('');

  // 历史记录相关的参数
  const historyId = ref<string>('');
  const historyVisible = ref(false);
  const historyLoading = ref(false);

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  // 创建进入页面时创建一个历史ID
  const createHistoryId = async () => {
    const res = await createHistory({ user_id: userId });
    console.log(res, 'res-createHistory');
    historyId.value = res.history_id;
  };

  onMounted(() => {
    createHistoryId();
  });

  // 获取模型列表
  const getHistoryList = async () => {
    if (historyId.value) {
      const data = await fetchHistoryList({
        user_id: userId,
        history_id: historyId.value,
      });
      console.log(data, 'ooooooooooooooooooo');
      historyList.value = data;
    }
  };

  onMounted(() => {
    getHistoryList();
  });

  // 格式化时间
  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
    }
  };

  // 过滤内容显示
  const filterContent = (item: IHistoryItems) => {
    // 查找第一条用户消息
    const firstUserMessage = item.messages.find((msg) => msg.role === 'user');
    if (firstUserMessage && firstUserMessage.content) {
      return firstUserMessage.content;
    }

    return '对话记录';
  };

  // 重置当前聊天
  const resetNowchat = (messages: Message[], historyId: string) => {
    currentSelectedId.value = historyId;
    // 将新的消息格式转换为当前使用的格式
    const convertedMessages = messages.map((msg) => ({
      type: msg.role === 'user' ? 'question' : 'answer',
      text: msg.content,
      image_url: msg.image_url_list?.[0] || '',
    }));
    state.messageList = convertedMessages;
    historyShow.value = false;
  };

  // 显示删除确认
  const showDeleteConfirm = (historyId: string, index: number, content: string) => {
    console.log('showDeleteConfirm called:', { historyId, index, content });
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除"${content}"这个历史会话吗？`,
      onOk: async () => {
        await deleteHistory(historyId);
        console.log('Delete confirmed for:', historyId);
        // historyList.value.splice(index, 1);
        // if (currentSelectedId.value === historyId) {
        //   currentSelectedId.value = '';
        //   state.messageList = [
        //     {
        //       type: 'answer',
        //       text: '你好，欢迎使用图文生视频功能，添加一张图片或输入你的描述，让我们一起创造属于你的视频世界吧，你可以发送输入框的内容试一下',
        //     },
        //   ];
        // }
        // // 保存到本地存储
        // localStorage.setItem('videoGenerationHistory', JSON.stringify(historyList.value));
        message.success('删除成功');
      },
    });
  };

  // 清空所有历史记录
  const clearAllHistory = () => {
    if (historyList.value.length === 0) {
      message.info('暂无历史记录可清空');
      return;
    }

    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有历史会话吗？此操作不可恢复！',
      onOk: () => {
        historyList.value = [];
        currentSelectedId.value = '';
        state.messageList = [
          {
            type: 'answer',
            text: '你好，欢迎使用图文生视频功能，添加一张图片或输入你的描述，让我们一起创造属于你的视频世界吧，你可以发送输入框的内容试一下',
          },
        ];
        localStorage.removeItem('videoGenerationHistory');
        message.success('历史记录已清空');
      },
    });
  };

  const sendMessage = useThrottle(async (text?: string) => {
    const value = typeof text === 'string' ? text : inputText.value;
    const image_url = uploadStyleImageUrl.value || '';
    if ((!value || !value.trim()) && !image_url) {
      message.warning('请添加图片或者输入描述内容');
      return;
    }

    // 确保使用正确的文本值，当只有图片时使用空字符串
    const question = typeof text === 'string' ? text : inputText.value;

    setTimeout(() => {
      inputText.value = '';
      console.log(1111111, inputText);
    }, 0);
    uploadStyleImageUrl.value = '';
    loading.value = true;

    // 将提问加入消息列表
    state.messageList.push({
      type: 'question',
      text: question || '',
      image_url: image_url,
    });

    loading.value = true;
    state.messageList.push({
      type: 'answer',
      text: '正在生成视频',
      icon: 'loading',
    });

    try {
      // 添加随机数到时间戳，确保每次请求都不同
      const timestamp = new Date().getTime() + Math.random();
      let res: any = await chatInHistory({
        user_id: userId,
        history_id: historyId.value,
        content: question || '',
        image_url_list: image_url ? null : [],
        _t: timestamp,
      });
      console.log(res, 'iiiiiiii');
      loading.value = true;
      state.messageList.pop();
      if (res === null || res === undefined || res.code === 50000) {
        state.messageList.push({
          type: 'answer',
          text: '视频生成失败',
        });
        loading.value = false;
      } else if (typeof res.video_url === 'string') {
        // 直接使用返回的视频URL
        state.messageList.push({
          type: 'answer',
          text: '已根据你的描述生成视频，快点击查看吧',
          video_url: res.video_url,
        });

        // 保存到历史记录
        saveToHistory();
      }
      loading.value = false;
    } catch {
      loading.value = false;
      state.messageList.pop();
      state.messageList.push({
        type: 'answer',
        text: '视频生成失败',
      });
    }
  }, 3000);

  // 保存到历史记录
  const saveToHistory = () => {
    if (state.messageList.length > 1) {
      // 至少有问答对
      // 将当前消息格式转换为新的格式
      const convertedMessages = state.messageList.map((msg) => ({
        role: msg.type === 'question' ? 'user' : 'assistant',
        content: msg.text,
        image_url_list: msg.image_url ? [msg.image_url] : [],
      }));

      const historyItem: IHistoryItems = {
        history_id: Date.now().toString(),
        messages: convertedMessages,
        updated_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
      };

      // 检查是否已存在相同的对话
      const existingIndex = historyList.value.findIndex(
        (item) => JSON.stringify(item.messages) === JSON.stringify(historyItem.messages),
      );

      if (existingIndex === -1) {
        historyList.value.unshift(historyItem);
        // 限制历史记录数量，最多保存50条
        if (historyList.value.length > 50) {
          historyList.value = historyList.value.slice(0, 50);
        }
        // 保存到本地存储
        localStorage.setItem('videoGenerationHistory', JSON.stringify(historyList.value));
      }
    }
  };

  // 加载历史记录
  const loadHistory = async () => {
    historyLoading.value = true;
    try {
      const savedHistory = localStorage.getItem('videoGenerationHistory');
      if (savedHistory) {
        historyList.value = JSON.parse(savedHistory);
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
      message.error('加载历史记录失败');
    } finally {
      historyLoading.value = false;
    }
  };

  // 处理历史会话图标点击
  const handleHistoryClick = () => {
    console.log('History list:', historyList.value);
    if (historyList.value.length === 0) {
      message.info('暂无历史会话记录');
      return;
    }
    historyShow.value = true;
  };
  //下载视频
  const downloadVideo = async (video_url: string) => {
    const imageURL = video_url;
    const response = await fetch(imageURL);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'downloaded_video.mp4'; // 为下载的图片指定一个文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const uploadStyleImageUrl = ref('');
  const styleUrlLoading = ref(false);

  const uploadStyleImageProps: UploadProps = {
    beforeUpload: (file: File) => {
      if (loading.value) {
        message.warning('视频生成中，请稍后再上传图片');
        return false;
      }
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: any) => {
      if (loading.value) {
        return;
      }
      const file = detail.file;
      const formData = new FormData();
      formData.append('face', file);
      styleUrlLoading.value = true;
      upload(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: string[]) => {
          uploadStyleImageUrl.value = data?.[0];
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          styleUrlLoading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const handleStyleImageChange = (info: any) => {
    if (info.file.status === 'done') {
      uploadStyleImageUrl.value = info.file.response.url;
    }
  };

  const clearUploadImage = () => {
    uploadStyleImageUrl.value = '';
  };

  onBeforeMount(async () => {
    const models = await getModelList();
    modelList.value = models?.['image-text-to-video'];
    activeModel.value = modelList.value[0];

    // 使用测试数据，不需要从localStorage加载
    // await loadHistory();
  });
</script>

<template>
  <div class="text-to-image-container">
    <!-- <div class="history" @click="handleHistoryClick">
      <a-tooltip title="历史会话" placement="top">
        <Icon name="lishijilu" size="24" />
      </a-tooltip>
    </div> -->
    <!-- 消息框 -->
    <div class="msg-box">
      <template v-for="(item, i) in state.messageList" :key="i">
        <!-- 问题展示 -->
        <div v-if="item.type === 'question'" class="question-box">
          <div class="text-box">
            <span class="text">{{ item.text }}</span>
            <a-image
              v-if="item.image_url"
              style="width: 200px"
              class="image-box"
              :src="item.image_url"
              :preview="false"
            />
          </div>
        </div>
        <!-- 回答展示 -->
        <div v-else-if="item.type === 'answer'" class="answer-box">
          <div class="text-right">
            <div style="display: flex; justify-content: flex-start; align-items: center">
              <template v-if="item.icon === 'loading'">
                <div class="loader" style="margin-right: 10px"></div>
              </template>
              <span>{{ item.text }}</span>
            </div>
            <div v-if="item.video_url" class="image-container">
              <video controls class="AImg" :src="item.video_url"></video>
              <div class="download-btn" @click="downloadVideo(item.video_url)">
                <DownloadOutlined style="fontsize: 24px" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 聊天框 -->
    <div class="chat-box">
      <div class="tip-box">
        <a-image class="tip-icon" style="width: 16px; height: 15px" :src="videoToImg" :preview="false" />
        <span class="title">图文生视频</span>
        <span class="divider"> | </span>
        <span class="text">添加一张图片、或输入文字，描述你想象的画面...AI帮你生成视频</span>
      </div>
      <div class="input-box">
        <div class="custom-upload-avatar">
          <a-upload
            v-bind="uploadStyleImageProps"
            list-type="picture-card"
            :class="['avatar-uploader', { 'uploader-disabled': loading }]"
            name="files"
            @change="handleStyleImageChange"
          >
            <template #default>
              <div class="upload-container">
                <div v-if="uploadStyleImageUrl" class="upload-preview">
                  <img :src="uploadStyleImageUrl" alt="avatar" class="avatar-icon" />
                  <div class="upload-overlay">
                    <UploadOutlined class="re-upload-icon" />
                    重新上传
                  </div>
                  <div class="delete-btn" @click.stop="clearUploadImage">
                    <CloseOutlined />
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <div class="upload-icon-box">
                    <button type="button" class="upload-button">
                      <template v-if="styleUrlLoading">
                        <LoadingOutlined />
                      </template>
                      <template v-else>
                        <a-image style="width: 16px; height: 15px" :src="uploadImage" :preview="false" />
                        <span>上传图片</span>
                      </template>
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </a-upload>
        </div>
        <a-textarea
          v-model:value="inputText"
          class="custom-textarea"
          placeholder="输入你的描述，继续创作视频"
          style="color: #17181a"
          :disabled="loading"
          :auto-size="{ minRows: uploadStyleImageUrl ? 4 : 1, maxRows: uploadStyleImageUrl ? 4 : 4 }"
          @change="
            (e: any) => {
              setInputValue(e.target.value);
            }
          "
          @press-enter="
            (e: any) => {
              e.preventDefault();
              sendMessage(inputText);
            }
          "
        />

        <div class="send-button" @mouseenter="sendBtnHover = true" @mouseleave="sendBtnHover = false">
          <a-image
            :src="btnImageSrc"
            alt=""
            :preview="false"
            :style="{ cursor: loading ? 'not-allowed' : 'pointer', bottom: uploadStyleImageUrl ? '-25px' : 0 }"
            style="width: 32px; height: 32px; cursor: pointer"
            @click="sendMessage(inputText)"
          />
        </div>
      </div>
    </div>
    <div class="footer">
      <span class="text-12px text-#999999 loading-16px font-400">以上内容均由AI生成，仅供参考</span>
    </div>

    <div class="model-nmae">
      <span>{{ activeModel?.name || 'black-forest-labs/FLUX.1-dev' }}</span>
      <a-tooltip placement="bottom" color="#fff" :overlay-inner-style="{ width: '320px' }">
        <template v-if="activeModel" #title><slot name="modelInfo" :value="activeModel"></slot></template>
        <InfoCircleOutlined class="info-icon" style="color: rgba(0, 0, 0, 0.35); cursor: pointer" />
      </a-tooltip>
    </div>
  </div>
  <History
    v-model:visible="historyVisible"
    :loading="historyLoading"
    :data="historyList"
    @fetch-data="getHistoryList"
  />
  <!-- <a-modal v-model:open="historyShow" title="历史会话" :footer="null" width="800px">
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
        <span>历史会话</span>
        <a-button v-if="historyList.length > 0" type="text" size="small" danger @click="clearAllHistory">
          清空全部
        </a-button>
      </div>
    </template>
    <div class="msgContent">
      <template v-if="historyLoading">
        <div style="display: flex; justify-content: center; align-items: center; height: 200px">
          <a-spin size="large" />
        </div>
      </template>
      <template v-else-if="historyList.length > 0">
        <div
          v-for="(item, index) in historyList"
          :key="index"
          class="msgItem"
          :class="{ 'msgItem-selected': currentSelectedId === item.history_id }"
          @click="resetNowchat(item.messages, item.history_id)"
        >
          <div class="showText">
            {{ filterContent(item) }}
          </div>
          <div>
            <span style="margin-right: 10px">{{ formatTime(item.updated_at) }}</span>
          </div>
          <DeleteOutlined class="del" @click.stop="showDeleteConfirm(item.history_id, index, filterContent(item))" />
        </div>
      </template>
      <a-empty v-else description="暂无历史会话" />
    </div>
  </a-modal> -->
</template>
<style lang="less" scoped>
  .text-to-image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px 20px 10px 20px;
    height: calc(100vh - 200px);
    overflow: hidden;

    .history {
      right: 30px;
      top: 5px;
      position: absolute;
      cursor: pointer;
      border: none;
      background: transparent;
      outline: none;
      padding: 0;
      margin: 0;

      &:hover {
        border: none;
        background: transparent;
        outline: none;
      }

      &:focus {
        border: none;
        background: transparent;
        outline: none;
      }

      &:active {
        border: none;
        background: transparent;
        outline: none;
      }

      // 确保图标本身没有边框效果
      :deep(.anticon) {
        border: none;
        background: transparent;
        outline: none;
      }

      // img {
      //   width: 40px; // 根据实际图片大小调整
      //   height: 40px;
      //   transition: all 0.2s ease; // 状态切换动画
      // }
    }

    .msg-box {
      width: 55%;
      padding: 10px 0;
      height: 100%;
      padding: 20px 0px 0 0px;
      overflow-y: auto;

      .answer-box {
        width: fit-content;
        display: flex;
        text-align: start;
        margin-bottom: 10px;
        border: 1px solid #ebebeb;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(216, 227, 243, 0.25);
        border-radius: 12px 12px 12px 0px;

        .text-right {
          width: fit-content;
          padding: 10px 15px;
          display: inline-block;
          background: #ffffff;
          border-radius: 12px 12px 12px 0px;
          text-align: left;
          font-size: 15px;
          .image-container {
            position: relative;
            display: flex;
            align-items: end;
            width: 100%;

            .AImg {
              width: 320px;
              border-radius: 8px;
              margin-top: 10px;
            }
            .download-btn {
              cursor: pointer;
              color: #797979;
              padding-left: 12px;
              border-radius: 8px;
              &:hover {
                color: #000;
              }
            }
          }

          > span {
            font-size: 15px;
            font-weight: 400;
            font-family:
              PingFangSC,
              PingFang SC;
            color: #17181a;
            line-height: 24px;

            font-style: normal;
          }
        }
      }

      .question-box {
        margin: 24px 0;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        text-align: start;

        .text-box {
          max-width: 90%;
          padding: 16px 10px;
          display: flex;
          flex-direction: column;
          gap: 10px;
          background: #dceefd;
          border-radius: 12px 12px 0px 12px;
          border: 1px solid #ffffff;
          opacity: 0.9;

          .text {
            font-size: 15px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            color: #17181a;
            line-height: 24px;
            text-align: justify;
            font-style: normal;
          }

          .image-box {
            height: auto;
            object-fit: cover;
            border-radius: 8px;
          }
        }

        > span {
          display: none;
        }
      }

      // /* 滚动条整体部分 */
      &::-webkit-scrollbar {
        height: 6px; /* 滚动条高度 */
        width: 6px; /* 滚动条宽度 */
      }
      // /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: transparent; /* 滑块颜色 */
        border-radius: 10px;
      }
      &:hover::-webkit-scrollbar-thumb {
        background-color: #888; /* 滑块颜色 */
      }
    }

    .chat-box {
      width: 55%; /* 占满页面宽度 */
      box-sizing: border-box; /* 包括内边距和边框 */
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      height: auto;
      background: #f2f8ff;
      box-shadow: 0px 4px 8px 0px rgba(191, 200, 213, 0.2);
      border-radius: 12px;

      .tip-box {
        width: 100%;
        height: 44px;
        padding: 10px 20px;
        display: flex;
        align-items: center;

        .title {
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #234772;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          padding: 1px 2px 0 7px;
        }
        .divider {
          width: 1px;
          height: 21px;
          color: #c9dfff;
          margin: 0 10px;
          border-radius: 1px;
        }
        .text {
          height: 24px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: rgba(35, 71, 114, 0.8);
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .input-box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 56px;
        background: #ffffff;
        border-radius: 0px 0px 12px 12px;
        border: none;
        padding: 16px;

        :deep(.ant-input) {
          border: none !important;
          box-shadow: none !important;
          outline: none !important;

          &:hover,
          &:focus {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        :deep(.ant-input-textarea) {
          border: none !important;
          box-shadow: none !important;

          &:hover,
          &:focus-within {
            border: none !important;
            box-shadow: none !important;
          }
        }

        :deep(.custom-textarea) {
          position: relative;
          font-family: PingFangSC, 'PingFang SC';
          font-size: 15px;
          font-weight: 400;
          color: #fff;
          background: rgb(255 255 255 / 20%);
          border-radius: 0px;
          // overflow: hidden;
          resize: none;
          padding: 0;
          padding: 0 5px;
          border: none;

          // &::-webkit-scrollbar {
          //   display: none;
          // }

          &::placeholder {
            color: #cccccc;
          }
        }

        .send-button {
          cursor: pointer;
          margin-left: 5px;
          // :deep(.ant-image) {
          //   position: relative !important;
          // }
        }

        // :deep(.ant-image:nth-of-type(1)) {
        //   position: absolute;
        //   right: 16px;
        //   bottom: 8px;
        //   width: 32px;
        //   height: 32px;
        //   cursor: pointer;
        // }
      }
    }

    .footer {
      margin-top: 12px;
      text-align: center;
    }

    :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select) {
      border: none;
      background: #f2f8ff;
      // padding: 1px;
      border-radius: 14px;
      margin-bottom: 0;
      height: auto;
    }

    .custom-upload-avatar {
      display: flex;
      flex-direction: column;

      .avatar-uploader {
        display: flex;
        flex-direction: column;

        &.uploader-disabled {
          cursor: not-allowed;
          opacity: 0.5;
          pointer-events: none;
        }

        .upload-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          background: #f2f8ff;
          padding: 5px;
          border-radius: 14px;
          // bottom: -55px;

          .upload-preview {
            position: relative;
            width: 80px;
            height: 80px;

            &:hover {
              .upload-overlay {
                opacity: 1;
              }
            }

            .avatar-icon {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 14px;
            }

            .delete-btn {
              position: absolute;
              top: -6px;
              right: -6px;
              width: 16px;
              height: 16px;
              background: black;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: white;
              font-size: 11px;
              z-index: 10;
            }

            .upload-overlay {
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
              font-size: 12px;
              font-weight: 400;
              line-height: 17px;
              color: white;
              background: rgb(0 0 0 / 50%);
              opacity: 0;
              transition: opacity 0.3s;
              border-radius: 14px;
              cursor: pointer;

              .re-upload-icon {
                width: 13px;
                height: 12px;
                margin-right: 4px;
              }
            }

            .preview-text {
              position: relative;
              left: 0;
              display: flex;
              align-items: center;
              background: #f2f8ff;
              border-radius: 0 0 14px 14px;
              gap: 4px;
              padding-left: 5px;
              width: 90px;
              left: -5px;

              span {
                font-size: 12px;
                color: #1777ff;
                line-height: 12px;
              }

              :deep(.ant-image) {
                position: relative !important;
              }
            }
          }

          .upload-placeholder {
            width: 80px;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f2f8ff;
            border-radius: 14px;

            .upload-icon-box {
              display: flex;
              flex-direction: column;
              align-items: center;

              .upload-button {
                background: transparent;
                border: 0;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 0;

                &:hover {
                  span {
                    color: #1890ff;
                  }
                }
                span {
                  font-size: 12px;
                  color: #1777ff;
                  line-height: 12px;
                }
              }
            }
          }
        }
      }
    }

    .loader {
      width: 24px;
      aspect-ratio: 1;
      border-radius: 50%;
      background:
        radial-gradient(farthest-side, #1777ff 94%, #0000) top/3px 3px no-repeat,
        conic-gradient(#0000 30%, #1777ff);
      -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 3px), #000 0);
      animation: l13 1s infinite linear;
    }
    @keyframes l13 {
      100% {
        transform: rotate(1turn);
      }
    }

    .model-nmae {
      position: absolute;
      background: #ffffff;
      box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
      border-radius: 2px;
      padding: 9px 12px;
      display: flex;
      justify-content: center;
      top: 21px;
      left: 8px;
      > span:nth-child(1) {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-right: 8px;
      }
    }
  }

  .msgContent {
    padding: 20px 0;
    max-height: 500px;
    overflow-y: auto;

    .msgItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60px;
      padding: 0 20px;
      border-radius: 8px;
      margin: 0 20px 8px 20px;
      cursor: pointer;
      transition: all 0.2s ease;

      &.msgItem-selected {
        background: #e6f7ff; // 选中背景色
        border-left: 3px solid #1890ff; // 选中左侧标识
      }

      &:hover:not(.msgItem-selected) {
        background: #f5f5f5;
      }

      &:active:not(.msgItem-selected) {
        background: #e8e8e8;
      }

      .showText {
        flex: 1;
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*禁止换行*/
        text-overflow: ellipsis; /*省略号*/
        font-size: 14px;
        color: #17181a;
        margin-right: 16px;
      }

      > div:last-child {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #8c8c8c;

        span {
          margin-right: 12px;
        }
      }

      .del {
        cursor: pointer;
        font-size: 14px;
        color: #f5222d; /* 红色更醒目 */
        display: none; /* 默认隐藏 */
        transition: all 0.2s ease; /* 过渡效果更自然 */
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: #fff1f0;
        }
      }

      // 鼠标移入时显示删除图标
      &:hover .del {
        display: inline-block;
        opacity: 1;
      }

      // 点击选中状态时也保持显示（配合之前的选中样式）
      &.msgItem-selected .del {
        display: inline-block;
        opacity: 1;
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
  :deep(.ant-upload.ant-upload-select) {
    width: 102px !important;
    height: auto !important;
  }
</style>
