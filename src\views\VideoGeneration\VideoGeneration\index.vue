<script setup lang="ts">
  import { ref, reactive, nextTick, onBeforeMount, computed } from 'vue';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendHover from '@/assets/image/base/pictures/sendHover.png.png';
  import sendDisable from '@/assets/image/base/pictures/sendDisable.png';
  import videoToImg from '@/assets/image/base/pictures/videoToImage.png';
  import uploadImage from '@/assets/image/base/pictures/upload-image.png';
  import { useThrottle } from '@/hooks/useThrottle';
  import { message } from 'ant-design-vue';
  import { sendMess } from '@/api/videoGeneration';
  import {
    UploadOutlined,
    LoadingOutlined,
    CloseOutlined,
    InfoCircleOutlined,
    DownloadOutlined,
  } from '@ant-design/icons-vue';
  import { getModelList, upload } from '@/api/textToImage';
  import type { UploadProps } from 'ant-design-vue';

  interface Message {
    type: 'question' | 'answer';
    text: string;
    answer?: string;
    video_url?: string;
    icon?: string;
    invalidText?: string;
    image_url?: string;
  }

  const defaultText = '帮我生成视频：小女孩在向日葵园玩耍';
  const btnImageSrc = computed(() => {
    let flag = sendBtn;
    if (inputText.value) {
      flag = loading.value ? sendDisable : sendBtnHover.value ? sendHover : sendBtn;
    } else {
      flag = sendDisable;
    }
    return flag;
  });
  // 输入框内容
  const inputText = ref(defaultText);
  const loading = ref(false);
  const sendBtnHover = ref(false);
  // 消息列表
  const state = reactive({
    messageList: [
      {
        type: 'answer',
        text: '你好，欢迎使用图文生视频功能，添加一张图片或输入你的描述，让我们一起创造属于你的视频世界吧，你可以发送输入框的内容试一下',
      },
    ] as Message[], // 消息列表
  });
  const setInputValue = (value: string) => {
    inputText.value = value;
  };

  const modelList = ref<any>([]);
  const activeModel = ref<any>({ name: 'Lightricks/LTX-Video' });

  const sendMessage = useThrottle(async (text?: string) => {
    const value = typeof text === 'string' ? text : inputText.value;
    const image_url = uploadStyleImageUrl.value || '';
    if ((!value || !value.trim()) && !image_url) {
      message.warning('请添加图片或者输入描述内容');
      return;
    }

    // 确保使用正确的文本值，当只有图片时使用空字符串
    const question = typeof text === 'string' ? text : inputText.value;

    setTimeout(() => {
      inputText.value = '';
      console.log(1111111, inputText);
    }, 0);
    uploadStyleImageUrl.value = '';
    loading.value = true;

    // 将提问加入消息列表
    state.messageList.push({
      type: 'question',
      text: question || '',
      image_url: image_url,
    });

    loading.value = true;
    state.messageList.push({
      type: 'answer',
      text: '正在生成视频',
      icon: 'loading',
    });

    try {
      // 添加随机数到时间戳，确保每次请求都不同
      const timestamp = new Date().getTime() + Math.random();
      let res: any = await sendMess({
        prompt: question || '',
        image_urls: [image_url],
        _t: timestamp,
      });
      loading.value = false;
      state.messageList.pop();
      if (res === null || res === undefined || res.code === 50000) {
        state.messageList.push({
          type: 'answer',
          text: '视频生成失败',
        });
        loading.value = false;
      } else if (typeof res.video_url === 'string') {
        // 直接使用返回的视频URL
        state.messageList.push({
          type: 'answer',
          text: '已根据你的描述生成视频，快点击查看吧',
          video_url: res.video_url,
        });
      }
      loading.value = false;
    } catch (error) {
      loading.value = false;
      state.messageList.pop();
      state.messageList.push({
        type: 'answer',
        text: '视频生成失败',
      });
    }
  }, 3000);
  //下载视频
  const downloadVideo = async (video_url: string) => {
    const imageURL = video_url;
    const response = await fetch(imageURL);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'downloaded_video.mp4'; // 为下载的图片指定一个文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const uploadStyleImageUrl = ref('');
  const styleUrlLoading = ref(false);

  const uploadStyleImageProps: UploadProps = {
    beforeUpload: (file: File) => {
      if (loading.value) {
        message.warning('视频生成中，请稍后再上传图片');
        return false;
      }
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: any) => {
      if (loading.value) {
        return;
      }
      const file = detail.file;
      const formData = new FormData();
      formData.append('face', file);
      styleUrlLoading.value = true;
      upload(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: string[]) => {
          uploadStyleImageUrl.value = data?.[0];
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          styleUrlLoading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const handleStyleImageChange = (info: any) => {
    if (info.file.status === 'done') {
      uploadStyleImageUrl.value = info.file.response.url;
    }
  };

  const clearUploadImage = () => {
    uploadStyleImageUrl.value = '';
  };

  onBeforeMount(async () => {
    const models = await getModelList();
    modelList.value = models?.['image-text-to-video'];
    activeModel.value = modelList.value[0];
  });
</script>

<template>
  <div class="text-to-image-container">
    <!-- 消息框 -->
    <div class="msg-box">
      <template v-for="(item, i) in state.messageList" :key="i">
        <!-- 问题展示 -->
        <div v-if="item.type === 'question'" class="question-box">
          <div class="text-box">
            <span class="text">{{ item.text }}</span>
            <a-image
              v-if="item.image_url"
              style="width: 200px"
              class="image-box"
              :src="item.image_url"
              :preview="false"
            />
          </div>
        </div>
        <!-- 回答展示 -->
        <div v-else-if="item.type === 'answer'" class="answer-box">
          <div class="text-right">
            <div style="display: flex; justify-content: flex-start; align-items: center">
              <template v-if="item.icon === 'loading'">
                <div class="loader" style="margin-right: 10px"></div>
              </template>
              <span>{{ item.text }}</span>
            </div>
            <div v-if="item.video_url" class="image-container">
              <video controls class="AImg" :src="item.video_url"></video>
              <div class="download-btn" @click="downloadVideo(item.video_url)">
                <DownloadOutlined style="fontsize: 24px" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 聊天框 -->
    <div class="chat-box">
      <div class="tip-box">
        <a-image class="tip-icon" style="width: 16px; height: 15px" :src="videoToImg" :preview="false" />
        <span class="title">图文生视频</span>
        <span class="divider"> | </span>
        <span class="text">添加一张图片、或输入文字，描述你想象的画面...AI帮你生成视频</span>
      </div>
      <div class="input-box">
        <div class="custom-upload-avatar">
          <a-upload
            v-bind="uploadStyleImageProps"
            list-type="picture-card"
            :class="['avatar-uploader', { 'uploader-disabled': loading }]"
            name="files"
            @change="handleStyleImageChange"
          >
            <template #default>
              <div class="upload-container">
                <div v-if="uploadStyleImageUrl" class="upload-preview">
                  <img :src="uploadStyleImageUrl" alt="avatar" class="avatar-icon" />
                  <div class="upload-overlay">
                    <UploadOutlined class="re-upload-icon" />
                    重新上传
                  </div>
                  <div class="delete-btn" @click.stop="clearUploadImage">
                    <CloseOutlined />
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <div class="upload-icon-box">
                    <button type="button" class="upload-button">
                      <template v-if="styleUrlLoading">
                        <LoadingOutlined />
                      </template>
                      <template v-else>
                        <a-image style="width: 16px; height: 15px" :src="uploadImage" :preview="false" />
                        <span>上传图片</span>
                      </template>
                    </button>
                  </div>
                </div>
              </div>
            </template>
          </a-upload>
        </div>
        <a-textarea
          v-model:value="inputText"
          class="custom-textarea"
          placeholder="输入你的描述，继续创作视频"
          style="color: #17181a"
          :disabled="loading"
          :auto-size="{ minRows: uploadStyleImageUrl ? 4 : 1, maxRows: uploadStyleImageUrl ? 4 : 4 }"
          @change="
            (e: any) => {
              setInputValue(e.target.value);
            }
          "
          @press-enter="
            (e: any) => {
              e.preventDefault();
              sendMessage(inputText);
            }
          "
        />

        <div class="send-button" @mouseenter="sendBtnHover = true" @mouseleave="sendBtnHover = false">
          <a-image
            :src="btnImageSrc"
            alt=""
            :preview="false"
            :style="{ cursor: loading ? 'not-allowed' : 'pointer', bottom: uploadStyleImageUrl ? '-25px' : 0 }"
            style="width: 32px; height: 32px; cursor: pointer"
            @click="sendMessage(inputText)"
          />
        </div>
      </div>
    </div>

    <div class="model-nmae">
      <span>{{ activeModel?.name || 'black-forest-labs/FLUX.1-dev' }}</span>
      <a-tooltip placement="bottom" color="#fff" :overlay-inner-style="{ width: '320px' }">
        <template v-if="activeModel" #title><slot name="modelInfo" :value="activeModel"></slot></template>
        <InfoCircleOutlined class="info-icon" style="color: rgba(0, 0, 0, 0.35); cursor: pointer" />
      </a-tooltip>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .text-to-image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px;
    height: calc(100vh - 200px);
    overflow: hidden;

    .msg-box {
      width: 55%;
      padding: 10px 0;
      height: 100%;
      padding: 20px 0px 0 0px;
      overflow-y: auto;

      .answer-box {
        width: fit-content;
        display: flex;
        text-align: start;
        margin-bottom: 10px;
        border: 1px solid #ebebeb;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(216, 227, 243, 0.25);
        border-radius: 12px 12px 12px 0px;

        .text-right {
          width: fit-content;
          padding: 10px 15px;
          display: inline-block;
          background: #ffffff;
          border-radius: 12px 12px 12px 0px;
          text-align: left;
          font-size: 15px;
          .image-container {
            position: relative;
            display: flex;
            align-items: end;
            width: 100%;

            .AImg {
              width: 320px;
              border-radius: 8px;
              margin-top: 10px;
            }
            .download-btn {
              cursor: pointer;
              color: #797979;
              padding-left: 12px;
              border-radius: 8px;
              &:hover {
                color: #000;
              }
            }
          }

          > span {
            font-size: 15px;
            font-weight: 400;
            font-family:
              PingFangSC,
              PingFang SC;
            color: #17181a;
            line-height: 24px;

            font-style: normal;
          }
        }
      }

      .question-box {
        margin: 24px 0;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        text-align: start;

        .text-box {
          max-width: 90%;
          padding: 16px 10px;
          display: flex;
          flex-direction: column;
          gap: 10px;
          background: #dceefd;
          border-radius: 12px 12px 0px 12px;
          border: 1px solid #ffffff;
          opacity: 0.9;

          .text {
            font-size: 15px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            color: #17181a;
            line-height: 24px;
            text-align: justify;
            font-style: normal;
          }

          .image-box {
            height: auto;
            object-fit: cover;
            border-radius: 8px;
          }
        }

        > span {
          display: none;
        }
      }

      // /* 滚动条整体部分 */
      &::-webkit-scrollbar {
        height: 6px; /* 滚动条高度 */
        width: 6px; /* 滚动条宽度 */
      }
      // /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: transparent; /* 滑块颜色 */
        border-radius: 10px;
      }
      &:hover::-webkit-scrollbar-thumb {
        background-color: #888; /* 滑块颜色 */
      }
    }

    .chat-box {
      width: 55%; /* 占满页面宽度 */
      box-sizing: border-box; /* 包括内边距和边框 */
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      height: auto;
      background: #f2f8ff;
      box-shadow: 0px 4px 8px 0px rgba(191, 200, 213, 0.2);
      border-radius: 12px;

      .tip-box {
        width: 100%;
        height: 44px;
        padding: 10px 20px;
        display: flex;
        align-items: center;

        .title {
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #234772;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          padding: 1px 2px 0 7px;
        }
        .divider {
          width: 1px;
          height: 21px;
          color: #c9dfff;
          margin: 0 10px;
          border-radius: 1px;
        }
        .text {
          height: 24px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: rgba(35, 71, 114, 0.8);
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .input-box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 56px;
        background: #ffffff;
        border-radius: 0px 0px 12px 12px;
        border: none;
        padding: 16px;

        :deep(.ant-input) {
          border: none !important;
          box-shadow: none !important;
          outline: none !important;

          &:hover,
          &:focus {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        :deep(.ant-input-textarea) {
          border: none !important;
          box-shadow: none !important;

          &:hover,
          &:focus-within {
            border: none !important;
            box-shadow: none !important;
          }
        }

        :deep(.custom-textarea) {
          position: relative;
          font-family: PingFangSC, 'PingFang SC';
          font-size: 15px;
          font-weight: 400;
          color: #fff;
          background: rgb(255 255 255 / 20%);
          border-radius: 0px;
          // overflow: hidden;
          resize: none;
          padding: 0;
          padding: 0 5px;
          border: none;

          // &::-webkit-scrollbar {
          //   display: none;
          // }

          &::placeholder {
            color: #cccccc;
          }
        }

        .send-button {
          cursor: pointer;
          margin-left: 5px;
          // :deep(.ant-image) {
          //   position: relative !important;
          // }
        }

        // :deep(.ant-image:nth-of-type(1)) {
        //   position: absolute;
        //   right: 16px;
        //   bottom: 8px;
        //   width: 32px;
        //   height: 32px;
        //   cursor: pointer;
        // }
      }
    }

    :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select) {
      border: none;
      background: #f2f8ff;
      // padding: 1px;
      border-radius: 14px;
      margin-bottom: 0;
      height: auto;
    }

    .custom-upload-avatar {
      display: flex;
      flex-direction: column;

      .avatar-uploader {
        display: flex;
        flex-direction: column;

        &.uploader-disabled {
          cursor: not-allowed;
          opacity: 0.5;
          pointer-events: none;
        }

        .upload-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          background: #f2f8ff;
          padding: 5px;
          border-radius: 14px;
          // bottom: -55px;

          .upload-preview {
            position: relative;
            width: 80px;
            height: 80px;

            &:hover {
              .upload-overlay {
                opacity: 1;
              }
            }

            .avatar-icon {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 14px;
            }

            .delete-btn {
              position: absolute;
              top: -6px;
              right: -6px;
              width: 16px;
              height: 16px;
              background: black;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: white;
              font-size: 11px;
              z-index: 10;
            }

            .upload-overlay {
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
              font-size: 12px;
              font-weight: 400;
              line-height: 17px;
              color: white;
              background: rgb(0 0 0 / 50%);
              opacity: 0;
              transition: opacity 0.3s;
              border-radius: 14px;
              cursor: pointer;

              .re-upload-icon {
                width: 13px;
                height: 12px;
                margin-right: 4px;
              }
            }

            .preview-text {
              position: relative;
              left: 0;
              display: flex;
              align-items: center;
              background: #f2f8ff;
              border-radius: 0 0 14px 14px;
              gap: 4px;
              padding-left: 5px;
              width: 90px;
              left: -5px;

              span {
                font-size: 12px;
                color: #1777ff;
                line-height: 12px;
              }

              :deep(.ant-image) {
                position: relative !important;
              }
            }
          }

          .upload-placeholder {
            width: 80px;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f2f8ff;
            border-radius: 14px;

            .upload-icon-box {
              display: flex;
              flex-direction: column;
              align-items: center;

              .upload-button {
                background: transparent;
                border: 0;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 0;

                &:hover {
                  span {
                    color: #1890ff;
                  }
                }
                span {
                  font-size: 12px;
                  color: #1777ff;
                  line-height: 12px;
                }
              }
            }
          }
        }
      }
    }

    .loader {
      width: 24px;
      aspect-ratio: 1;
      border-radius: 50%;
      background:
        radial-gradient(farthest-side, #1777ff 94%, #0000) top/3px 3px no-repeat,
        conic-gradient(#0000 30%, #1777ff);
      -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 3px), #000 0);
      animation: l13 1s infinite linear;
    }
    @keyframes l13 {
      100% {
        transform: rotate(1turn);
      }
    }

    .model-nmae {
      position: absolute;
      background: #ffffff;
      box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
      border-radius: 2px;
      padding: 9px 12px;
      display: flex;
      justify-content: center;
      top: 21px;
      left: 8px;
      > span:nth-child(1) {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-right: 8px;
      }
    }
  }
  :deep(.ant-upload.ant-upload-select) {
    width: 102px !important;
    height: auto !important;
  }
</style>
