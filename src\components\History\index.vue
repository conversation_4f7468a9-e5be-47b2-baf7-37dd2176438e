<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import type { IHistoryItems } from '.';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  interface IProps {
    visible: boolean;
    data: IHistoryItems[];
    loading: boolean;
  }
  const props = withDefaults(defineProps<IProps>(), {
    visible: false,
  });
  const emits = defineEmits<{
    (event: 'update:visible', value: boolean): void;
    (event: 'fetchData'): void;
  }>();
  const historyRef = ref();
  const historyIconRef = ref();
  const handleClick = (e: Event) => {
    // 如果点击了目标元素以外的元素，关闭弹窗
    if (historyIconRef.value.contains(e.target)) {
      emits('update:visible', true);
      if (!props.visible) {
        emits('fetchData');
      }
    } else if (!historyRef.value.contains(e.target)) {
      emits('update:visible', false);
    }
  };

  /**
   * 判断时间是否是今天
   * @param isoTime ISO时间字符串
   * @returns 是否是今天
   */
  function isToday(isoTime: string): boolean {
    if (!isoTime) return false;

    const inputDate = new Date(isoTime);
    const today = new Date();

    return (
      inputDate.getFullYear() === today.getFullYear() &&
      inputDate.getMonth() === today.getMonth() &&
      inputDate.getDate() === today.getDate()
    );
  }

  /**
   * 判断时间是否是昨天
   * @param isoTime ISO时间字符串
   * @returns 是否是昨天
   */
  function isYesterday(isoTime: string): boolean {
    if (!isoTime) return false;

    const inputDate = new Date(isoTime);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return (
      inputDate.getFullYear() === yesterday.getFullYear() &&
      inputDate.getMonth() === yesterday.getMonth() &&
      inputDate.getDate() === yesterday.getDate()
    );
  }
  const formatDate = (date: string) => {
    let time = '';
    if (isToday(date)) {
      time = '今天';
    } else if (isYesterday(date)) {
      time = '昨天';
    } else {
      time = convertIsoTimeToLocalTime(date);
    }
    return time;
  };
  const handleClickHistory = () => {};
  onMounted(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.removeEventListener('click', handleClick);
    };
  });
</script>

<template>
  <div class="history">
    <a-tooltip>
      <template #title> 历史会话 </template>
      <div ref="historyIconRef">
        <svg class="history-icon" aria-hidden="true">
          <use xlink:href="#icon-lishijilu"></use>
        </svg>
      </div>
    </a-tooltip>
  </div>
  <div v-show="visible" ref="historyRef" class="hitory-wrapper w-100% h-100%">
    <div class="header">
      <div class="header-content">
        <div>历史会话</div>
        <span class="text-14px text-#797979 ml-10px">仅保留最近30条对话记录，每条记录至多进行30轮对话</span>
      </div>
      <div class="close"><CloseOutlined /></div>
    </div>
    <a-spin :spinning="loading">
      <div class="content overflow-scroll">
        <div v-for="item in data" :key="item.id" class="history-item" @click="handleClickHistory">
          <div class="title">{{ item.messages[0] ? item.messages[0].content : '--' }}</div>
          <div class="date">{{ formatDate(item.updated_at) }}</div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
  .history {
    position: absolute;
    right: 0;
    top: 0;
    .history-icon {
      width: 24px;
      height: 24px;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }
  .hitory-wrapper {
    position: absolute;
    right: 0;
    top: 30px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: #fff;
    width: 40%;
    height: 500px;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ccc;
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > div {
        font-size: 16px;
        font-weight: bold;
      }
    }
    .close {
      cursor: pointer;
    }
  }
  .content {
    height: 440px;
    padding: 10px;
    .history-item {
      height: 60px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      &:hover {
        background-color: #f6f8fb;
        .title {
          color: #0046d7;
        }
      }
    }
  }
</style>
