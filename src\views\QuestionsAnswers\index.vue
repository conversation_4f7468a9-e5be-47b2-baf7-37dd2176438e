<script setup lang="ts">
  import { ref, onMounted, reactive, watch, nextTick, onUnmounted } from 'vue';
  import { onBeforeRouteLeave } from 'vue-router';

  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendHover from '@/assets/image/base/pictures/sendHover.png.png';
  import sendDisable from '@/assets/image/base/pictures/sendDisable.png';
  import { useThrottle } from '@/hooks/useThrottle';
  import { historyId, TTS, recommandation } from '@/api/questionsAnswers';
  import { message } from 'ant-design-vue';
  import { RightCircleOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import { getEnv } from '@/utils';
  import { marked } from 'marked';
  import welcome from '@/assets/video/welcomeAudio.wav';
  import AudioQueue from './AudioQueue.vue';
  import { onBeforeMount } from 'vue';
  import { getModelList } from '@/api/textToImage';

  // 在 setup 里
  interface Message {
    type: 'question' | 'answer';
    text: string;
    answer?: string;
    video_url?: string;
    icon?: string;
    invalidText?: string;
    isNav?: boolean;
    isRecommended?: boolean;
    // recommendedList?: any[];
  }

  const { VITE_APP_AVATAR_URL } = getEnv();

  let welcomePlayed = false;
  const welcomeAudio = ref<HTMLAudioElement | null>(null);
  const audioRef = ref<any>(null);
  const topicId = ref<string>('');
  const defaultText = '';
  // 输入框内容
  const inputText = ref(defaultText);
  const loading = ref(false);
  const sendBtnHover = ref(false);
  const navList = ref<any>([{ title: '剑门关特色' }, { title: '剑门关景区介绍' }, { title: '剑门关历史背景' }]);
  const recommendedList = ref<any>([]);
  const audioList = ref<string[]>([]);

  const modelList = ref<any>([]);
  const activeModel = ref<any>({ name: '' });

  const renderMarkdown = (md: string) => marked(md || '');

  // 消息列表
  const state = reactive({
    messageList: [
      {
        type: 'answer',
        text: '哈喽，我是剑门关景区助手小关，有什么可以帮助您的吗，您可以试着问我',
        isNav: true,
        isRecommended: false,
      },
    ] as Message[], // 消息列表
  });
  const setInputValue = (value: string) => {
    inputText.value = value;
  };

  const getHistoryId = async () => {
    const data = await historyId();
    topicId.value = data.history_id;
  };

  const playWelcome = () => {
    if (!welcomePlayed) {
      welcomeAudio.value = new Audio(welcome);
      welcomeAudio.value.play();
      welcomePlayed = true;
    }
  };

  // 清理所有音频的函数
  const cleanupAllAudio = () => {
    console.log('Cleaning up all audio...'); // 添加日志
    // 停止欢迎语音
    if (welcomeAudio.value) {
      welcomeAudio.value.pause();
      welcomeAudio.value.currentTime = 0;
      welcomeAudio.value = null;
    }
    // 清空音频队列并停止当前播放
    if (audioRef.value) {
      console.log('Stopping audio queue...'); // 添加日志
      audioRef.value.stopAllAudio();
    }
    audioList.value = [];
    // 重置欢迎语音状态
    welcomePlayed = false;
  };

  // 路由离开前处理
  onBeforeRouteLeave(() => {
    console.log('Route leaving, cleaning up audio...'); // 添加日志
    cleanupAllAudio();
  });

  // 组件卸载时处理
  onUnmounted(() => {
    console.log('Component unmounting, cleaning up audio...'); // 添加日志
    cleanupAllAudio();
    // 移除页面可见性监听
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  });

  // 页面可见性变化处理函数
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      playWelcome();
    } else {
      console.log('Page hidden, cleaning up audio...'); // 添加日志
      cleanupAllAudio();
    }
  };

  // 监听页面可见性变化
  onMounted(() => {
    setTimeout(() => {
      getHistoryId();
    });

    // 优先尝试自动播放
    playWelcome();

    // 添加页面可见性监听
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });

  onBeforeMount(async () => {
    const models = await getModelList();
    modelList.value = models?.['text-to-text'];
    activeModel.value = modelList.value[0];
  });

  const getRecommandation = async (question: string, llm_answer: string) => {
    const data = await recommandation({ question: question, llm_answer: llm_answer });
    recommendedList.value = data.map((item: any) => ({ title: item.trim() }));
  };

  //点击导航
  const clickNav = (item: any) => {
    sendMessage(item.title);
  };

  const sendMessage = useThrottle(async (text?: string) => {
    // 停止之前的音频播放
    cleanupAllAudio();

    state.messageList[state.messageList.length - 1].isRecommended = false;

    let value = typeof text === 'string' ? text : inputText.value;
    if (!value || !value.trim()) {
      message.warning('请输入描述内容');
      return;
    }
    let question = text || inputText;
    nextTick(() => {
      inputText.value = '';
    });
    loading.value = true;
    // 清空语音队列
    audioList.value = [];

    // 拼接 GET 请求参数
    const params = new URLSearchParams({
      question: value,
      history_id: topicId.value,
    }).toString();

    // 每次新建 EventSource 实例
    const sse = new EventSource(`${VITE_APP_AVATAR_URL}/intelligent-customer-service/jianmen_pass/chat?${params}`);

    // 用户问题
    state.messageList.push({
      type: 'question',
      text: typeof question === 'string' ? question : question.value,
    });

    state.messageList.push({
      type: 'answer',
      text: '正在思考',
      icon: 'loading',
    });

    let isFirst = true; // 用于判断是否是第一次接收消息
    let lastMessage = state.messageList[state.messageList.length - 1]; // 用于存储最后一条消息的内容
    sse.onmessage = (ev) => {
      loading.value = false; // 重置标志位
      lastMessage.icon = undefined;
      if (isFirst) {
        lastMessage.text = ''; // 清空之前的内容
        isFirst = false; // 设置为 false，表示已经接收过消息
      }
      lastMessage.text += ev.data;

      if (ev.data === '[DONE]') {
        state.messageList[state.messageList.length - 1].text = lastMessage.text.slice(0, -6); // 去掉最后的 [DONE]
        getRecommandation(value, lastMessage.text);
        lastMessage.isRecommended = true;
        sse.close();
        TTSPlay(lastMessage.text);
        return;
      }
    };

    sse.onerror = () => {
      sse.close();
      loading.value = false;
    };
  }, 3000);

  const TTSPlay = async (text: string) => {
    const data = await TTS({ text: text, speaker: 'xingxing' });
    // 设置音频列表（实际使用时替换为真实的音频URL）
    audioList.value = [...audioList.value, ...data.urls];
  };

  const msgBox = ref<HTMLElement | null>(null);
  watch(
    () => state.messageList,
    () => {
      nextTick(() => {
        if (msgBox.value) {
          msgBox.value.scrollTop = msgBox.value.scrollHeight;
        }
      });
    },
    { deep: true },
  );
</script>

<template>
  <div class="text-to-image-container">
    <!-- 消息框 -->
    <div ref="msgBox" class="msg-box">
      <template v-for="(item, i) in state.messageList" :key="i">
        <!-- 问题展示 -->
        <div v-if="item.type === 'question'" class="question-box">
          <span class="text">{{ item.text }}</span>
        </div>
        <!-- 回答展示 -->
        <div v-else-if="item.type === 'answer'" class="answer-box">
          <div class="text-right">
            <div style="display: flex; justify-content: flex-start; align-items: center">
              <div v-if="item.icon === 'loading'" class="loader"></div>
              <span v-if="item.icon === 'loading'" style="text-align: center">正在思考</span>
              <span v-if="item.icon === undefined" v-html="renderMarkdown(item.text)"></span>
            </div>
            <div v-if="item.isNav" class="nav">
              <div v-for="(nav, index) in navList" :key="index" class="navItem" @click="clickNav(nav)">
                <a-button
                  type="link"
                  :disabled="loading === true ? true : false"
                  style="width: 240px; position: relative; display: flex; justify-content: flex-start"
                >
                  {{ nav.title }}
                  <RightCircleOutlined
                    class="nav-icon"
                    style="float: right; position: absolute; margin-top: 5px; right: 16px"
                  />
                </a-button>
              </div>
            </div>

            <div v-if="item.video_url" class="image-container">
              <!-- <video style="width: 200px; height: 356px" controls class="AImg" :src="item.video_url"></video> -->
            </div>
          </div>
          <div v-if="item.isRecommended" class="recommended">
            <span class="title">继续提问</span>
            <div
              v-for="(recommended, index) in recommendedList"
              :key="index"
              class="recommandedItem"
              @click="clickNav(recommended)"
            >
              <div>{{ recommended.title }}</div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <img
      class="logo"
      src="https://prod.shukeyun.com/maintenance/deepfile/data/2025-05-22/upload_bb537ff6f26e5bd7bc8cb7c0c6980616.png"
    />

    <AudioQueue ref="audioRef" :audio-urls="audioList" />
    <!-- 聊天框 -->
    <div class="chat-box">
      <div class="input-box">
        <a-textarea
          v-model:value="inputText"
          class="custom-textarea overflow-scroll"
          placeholder="请输入你想问的问题"
          :auto-size="{ minRows: 1, maxRows: 4 }"
          style="color: #17181a"
          :disabled="loading"
          @change="
            (e: any) => {
              setInputValue(e.target.value);
            }
          "
          @press-enter="
            (e: any) => {
              e.preventDefault();
              sendMessage(inputText);
            }
          "
        />

        <a-image
          class="send-icon"
          :src="loading ? sendDisable : sendBtnHover ? sendHover : sendBtn"
          alt=""
          :preview="false"
          :style="{ cursor: loading ? 'not-allowed' : 'pointer' }"
          @mouseenter="sendBtnHover = true"
          @mouseleave="sendBtnHover = false"
          @click="sendMessage(inputText)"
        />
      </div>
    </div>

    <div class="model-nmae">
      <span>{{ activeModel?.name || 'Qwen/Qwen2.5-72B' }}</span>
      <a-tooltip placement="bottom" color="#fff" :overlay-inner-style="{ width: '320px' }">
        <template v-if="activeModel" #title>
          <div class="model-info">
            <div>{{ activeModel.name }} 基本信息</div>
            <div>
              <a-tag v-for="(t, i) in activeModel.tags" :key="i" color="cyan">{{ t }}</a-tag>
            </div>
            <div>{{ activeModel.description }}</div>
          </div>
        </template>
        <InfoCircleOutlined class="info-icon" style="color: rgba(0, 0, 0, 0.35); cursor: pointer" />
      </a-tooltip>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .text-to-image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100vh - 240px);
    .logo {
      position: absolute;
      top: 80px;
      left: 145px;
      width: 197px;
      height: 157px;
    }
    .msg-box {
      width: 60%;
      padding: 20px 0;
      height: 100%;
      padding: 80px 25px 0 25px;
      overflow-y: auto;

      .answer-box {
        width: 100%;
        display: flex;
        text-align: start;
        margin-bottom: 10px;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;

        .text-right {
          max-width: 100%;
          display: inline-block;
          background: #ffffff;
          border-radius: 12px 12px 12px 0px;
          text-align: left;
          border: 1px solid #ebebeb;
          background: #ffffff;
          line-height: 24px;
          box-shadow: 0px 4px 8px 0px rgba(216, 227, 243, 0.25);
          padding: 16px 20px;

          .nav {
            width: 250px;
            font-size: 14px;
            color: #1777ff;
            height: 120px;
            margin-top: 6px;
            flex-direction: column;
            display: flex;
            line-height: 14px;
            justify-content: space-around;
            .navItem {
              width: 100%;
              // padding: 9px 16px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: #f2f8ff;
              border-radius: 16px;
              cursor: pointer;
            }
          }
          .image-container {
            position: relative;
            width: 100%;
          }

          > span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 15px;
            color: #17181a;
            line-height: 24px;
            text-align: left;
            font-style: normal;
          }

          :deep(p) {
            margin-bottom: 0 !important;
          }
        }

        .recommended {
          font-size: 14px;
          color: #1777ff;
          height: 145px;
          margin-top: 6px;
          flex-direction: column;
          display: flex;
          line-height: 14px;
          justify-content: space-around;

          .title {
            width: 48px;
            height: 17px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #636466;
            line-height: 17px;
            text-align: left;
            opacity: 0.7;
            font-style: normal;
            margin: 2px 0 4px 2px;
          }
          .title :hover {
            background: #ffffff !important;
          }
          .recommandedItem {
            width: 100%;
            padding: 9px 16px;
            display: flex;
            height: 32px;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
            border-radius: 16px;
            cursor: pointer;
            border: 1px solid rgba(23, 119, 255, 0.4);
          }
          :hover {
            background: #f2f8ff;
          }
        }
      }

      .question-box {
        margin: 10px 0;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        text-align: start;

        > span {
          max-width: 90%;
          padding: 10px 15px;
          display: inline-block;
          background: #dceefd;
          border-radius: 12px 12px 0px 12px;
          border: 1px solid #ffffff;
          opacity: 0.9;
          font-size: 15px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          color: #17181a;
          line-height: 24px;
          text-align: justify;
          font-style: normal;
        }
      }

      // /* 滚动条整体部分 */
      &::-webkit-scrollbar {
        height: 6px; /* 滚动条高度 */
        width: 6px; /* 滚动条宽度 */
      }
      // /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: transparent; /* 滑块颜色 */
        border-radius: 10px;
      }
      &:hover::-webkit-scrollbar-thumb {
        background-color: rgba(136, 136, 136, 0.5); /* 滑块颜色 */
      }
    }

    .chat-box {
      width: 60%; /* 占满页面宽度 */
      box-sizing: border-box; /* 包括内边距和边框 */
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      height: auto;
      background: #f2f8ff;
      border-radius: 12px;
      .input-box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: auto;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(184, 196, 213, 0.2);
        border-radius: 12px;
        border: 1px solid #e9edf2;
        :deep(.custom-textarea) {
          position: relative;
          box-sizing: border-box;
          height: auto;
          padding: 10px;
          font-family: PingFangSC, 'PingFang SC';
          font-size: 16px;
          font-weight: 400;
          color: #fff;
          background: rgb(255 255 255 / 20%);
          border: 1px solid #e9edf2;
          border-radius: 12px;
          resize: none; /* 禁止用户调整大小 */

          box-shadow: 0px 4px 8px 0px rgba(184, 196, 213, 0.2);

          /* 占位符样式 */
          &::placeholder {
            color: #cccccc;
          }
        }

        :deep(.ant-image:nth-of-type(1)) {
          position: absolute;
          right: 16px; /* 第二个 .ant-image 距离右侧 10px */
          bottom: 8px;
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }
    }
    .loader {
      width: 21px;
      aspect-ratio: 1;
      margin-right: 10px;
      border-radius: 50%;
      background:
        radial-gradient(farthest-side, #1777ff 94%, #0000) top/3px 3px no-repeat,
        conic-gradient(#0000 30%, #1777ff);
      -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 3px), #000 0);
      animation: l13 1s infinite linear;
    }
    @keyframes l13 {
      100% {
        transform: rotate(1turn);
      }
    }

    .model-nmae {
      position: absolute;
      background: #ffffff;
      box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
      border-radius: 2px;
      padding: 9px 12px;
      display: flex;
      justify-content: center;
      top: 8px;
      left: 8px;
      > span:nth-child(1) {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-right: 8px;
      }
    }
  }
  .model-info {
    color: #000 !important;
    > div:nth-child(1) {
      white-space: nowrap;
      padding: 0 20px 0 0;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
      letter-spacing: 0;
      margin-bottom: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    > div:nth-child(2) {
      margin-bottom: 10px;
    }
    > div:nth-child(3) {
      width: 100%;
      height: 60px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: grey;
      letter-spacing: 0;
    }
  }
</style>
